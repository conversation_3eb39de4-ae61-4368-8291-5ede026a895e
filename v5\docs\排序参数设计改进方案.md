# 排序参数设计改进方案

## 问题分析

### 当前设计存在的问题

1. **字段配置与参数配置不一致**
   - 字段配置：只有 `sortable: true/false`
   - 参数配置：`sort` 和 `order` 分离配置
   - 缺少字段级别的默认排序方向配置

2. **参数配置问题**
   - `sort` 参数默认值 `'id'` 不准确（实际字段可能是 `F_ID`）
   - `sort` 参数没有从 ORM 配置获取可选字段
   - `order` 参数缺少枚举约束（应限制为 `asc`/`desc`）

3. **用户体验问题**
   - 用户需要手动输入字段名，容易出错
   - 不同类型字段没有智能的默认排序方向
   - 参数配置界面缺少字段关联提示

## 已实施的改进

### 1. 常用参数配置重构 ✅

**移除的参数：**
- ~~`page`~~ - 页码（后端已有分页逻辑）
- ~~`size`~~ - 每页数量（后端已有分页逻辑）
- ~~`status`~~ - 状态筛选（与启用/禁用重复）
- ~~`sort`~~ - 排序字段（移到可视化配置）
- ~~`order`~~ - 排序方向（移到可视化配置）
- ~~`id`~~ - 路径参数（根据路由配置自动处理）
- ~~`token`~~ - 认证令牌（改为标准的Authorization）

**新增的参数：**
```javascript
// Query 参数
keyword: {
  name: 'keyword',
  type: 'query',
  dataType: 'string',
  description: '搜索关键词（配合字段模糊查询使用）'
},

// Header 参数 - 认证相关
Authorization: {
  name: 'Authorization',
  type: 'header',
  dataType: 'string',
  description: '认证令牌（如：Bearer token）'
},
'X-API-Key': {
  name: 'X-API-Key',
  type: 'header',
  dataType: 'string',
  description: 'API密钥'
},

// Header 参数 - 内容类型
'Content-Type': {
  name: 'Content-Type',
  type: 'header',
  dataType: 'enum',
  options: ['application/json', 'application/xml', ...]
},
'Accept': {
  name: 'Accept',
  type: 'header',
  dataType: 'enum',
  options: ['application/json', 'application/xml', ...]
},

// Header 参数 - 业务相关
'X-Request-ID': {
  name: 'X-Request-ID',
  type: 'header',
  dataType: 'string',
  description: '请求追踪ID'
},
'User-Agent': {
  name: 'User-Agent',
  type: 'header',
  dataType: 'string',
  description: '用户代理标识'
}
```

### 2. 可视化配置增强 ✅

**新增排序方向字段：**
- 在字段配置表格中增加"排序方向"列
- 提供升序(asc)/降序(desc)选择器
- 只有启用排序的字段才能设置排序方向
- 智能默认值：ID和时间类字段默认降序，其他字段默认升序

**字段配置结构扩展：**
```javascript
{
  name: "F_ID",
  sortable: true,
  sortOrder: "desc",  // 新增：排序方向
  searchable: true,
  filterable: true,
  rangeable: false
}
```

### 3. 用户界面优化 ✅

**常用参数下拉菜单重构：**
- 按功能分组显示（Query参数、认证相关、内容类型、业务相关）
- 使用分隔线区分不同类型的参数
- 更清晰的参数描述文本

## 建议的进一步改进

### 阶段二：字段级排序配置

**1. 扩展字段配置结构**
```json
{
  "name": "F_ID",
  "sortable": true,
  "default_sort_order": "desc",  // 新增：默认排序方向
  "sort_priority": 1             // 新增：排序优先级（可选）
}
```

**2. 智能默认值规则**
- ID 类字段：默认 `desc`（最新记录在前）
- 时间类字段：默认 `desc`（最新时间在前）
- 名称类字段：默认 `asc`（字母顺序）
- 状态类字段：默认 `asc`（按状态码顺序）

### 阶段三：参数配置界面增强

**1. 字段关联选择器**
- `sort` 参数提供下拉选择，从 ORM 配置的 `allowed_sort_fields` 获取选项
- 选择 `sort` 字段后，自动设置对应的 `order` 默认值

**2. 参数模板功能**
- 提供排序参数组合模板：`sort + order`
- 提供分页参数组合模板：`page + size + sort + order`
- 提供搜索参数组合模板：`keyword + status + sort + order`

### 阶段四：ORM 配置生成优化

**1. 改进 query_mapping 结构**
```json
"query_mapping": {
  "allowed_sort_fields": {
    "F_ID": { "default_order": "desc", "priority": 1 },
    "F_NAME": { "default_order": "asc", "priority": 2 },
    "F_STATE": { "default_order": "asc", "priority": 3 }
  },
  "default_sort": "F_ID",
  "default_order": "desc"
}
```

**2. 自动生成排序参数**
- 根据 ORM 配置自动生成 `sort` 和 `order` 参数
- 根据字段类型智能推荐默认排序方向

## 实施建议

### 优先级 1（已完成 ✅）
- [x] 重构常用参数配置
- [x] 增加可视化配置中的排序方向字段
- [x] 改进用户界面文本和分组显示
- [x] 实现智能默认排序方向推荐

### 优先级 2（短期实施）
- [ ] 添加参数验证逻辑
- [ ] 实现参数组合模板功能
- [ ] 优化ORM配置生成时的排序配置处理

### 优先级 3（长期规划）
- [ ] 完善参数配置文档自动生成
- [ ] 增加更多常用请求头参数
- [ ] 实现参数配置的导入导出功能

## 技术实现要点

1. **向后兼容性**：确保现有配置仍然有效
2. **渐进式改进**：分阶段实施，避免大规模重构
3. **用户体验**：提供清晰的配置指导和错误提示
4. **文档同步**：及时更新相关文档和示例

## 测试验证

1. **功能测试**：验证排序参数的正确性
2. **兼容性测试**：确保现有接口配置正常工作
3. **用户体验测试**：收集用户对新界面的反馈
4. **性能测试**：验证参数配置对接口性能的影响
