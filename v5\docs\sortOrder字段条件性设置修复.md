# sortOrder 字段条件性设置修复

## 问题描述

用户反馈：可视化配置中，如果没有启用排序（`sortable: false`），则不应该把排序字段写入到 ORM 配置中。`"sortOrder": "asc"` 这个字段不应该在未启用排序的字段中出现。

## 问题分析

### 根本原因

在字段初始化的多个位置，代码无条件地为所有字段设置了 `sortOrder` 属性，而不管 `sortable` 是否为 `true`：

1. **从表结构生成字段时**：给所有字段都设置了 `sortOrder: 'desc'`
2. **从ORM配置覆盖字段时**：无条件设置 `sortOrder`
3. **处理已删除字段时**：也无条件设置 `sortOrder`

### 问题影响

- 未启用排序的字段也会在ORM配置中包含 `sortOrder` 属性
- 生成的ORM配置包含不必要的排序信息
- 可能导致后端处理时的混淆

## 修复方案

### 核心原则

**只有在 `sortable: true` 时才设置 `sortOrder` 属性**

### 修复位置

#### 1. 从表结构生成字段时

**修复前：**
```javascript
// 默认状态：从表结构来的字段默认禁用
enabled: false,
searchable: false,
filterable: false,
rangeable: false,
sortable: false,
sortOrder: 'desc', // 默认降序 ❌ 无条件设置
source: 'table_structure',
status: 'from_table'
```

**修复后：**
```javascript
// 默认状态：从表结构来的字段默认禁用
enabled: false,
searchable: false,
filterable: false,
rangeable: false,
sortable: false,
// 不设置sortOrder，只有启用排序时才设置 ✅
source: 'table_structure',
status: 'from_table'
```

#### 2. 从ORM配置覆盖字段状态时

**修复前：**
```javascript
sortable: queryMapping?.allowedSortFields?.includes(ormField.name) ||
         queryMapping?.allowed_sort_fields?.includes(ormField.name) || false,
sortOrder: ormField.sortOrder || ormField.sort_order ||
          (ormField.name.toLowerCase().includes('id') ||
           ormField.name.toLowerCase().includes('time') ||
           ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc'), // ❌ 无条件设置
```

**修复后：**
```javascript
const isSortable = queryMapping?.allowedSortFields?.includes(ormField.name) ||
                  queryMapping?.allowed_sort_fields?.includes(ormField.name) || false;

const fieldConfig: any = {
  // ... 其他属性
  sortable: isSortable,
  // ... 其他属性
};

// 只有在启用排序时才设置排序方向 ✅
if (isSortable) {
  fieldConfig.sortOrder = ormField.sortOrder || ormField.sort_order ||
                         (ormField.name.toLowerCase().includes('id') ||
                          ormField.name.toLowerCase().includes('time') ||
                          ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc');
}
```

#### 3. 处理已删除字段时

**修复前：**
```javascript
sortable: queryMapping?.allowedSortFields?.includes(ormField.name) ||
         queryMapping?.allowed_sort_fields?.includes(ormField.name) || false,
sortOrder: ormField.sortOrder || ormField.sort_order ||
          (ormField.name.toLowerCase().includes('id') ||
           ormField.name.toLowerCase().includes('time') ||
           ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc'), // ❌ 无条件设置
```

**修复后：**
```javascript
const isSortable = queryMapping?.allowedSortFields?.includes(ormField.name) ||
                  queryMapping?.allowed_sort_fields?.includes(ormField.name) || false;

const fieldConfig: any = {
  // ... 其他属性
  sortable: isSortable,
  // ... 其他属性
};

// 只有在启用排序时才设置排序方向 ✅
if (isSortable) {
  fieldConfig.sortOrder = ormField.sortOrder || ormField.sort_order ||
                         (ormField.name.toLowerCase().includes('id') ||
                          ormField.name.toLowerCase().includes('time') ||
                          ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc');
}
```

#### 4. 保存配置时的逻辑（已经是正确的）

**现有逻辑（保持不变）：**
```javascript
// 只有在启用排序时才添加排序方向 ✅
if (field.sortable) {
  fieldConfig.sortOrder = field.sortOrder || 'desc';
}
```

## 修复效果

### 1. ORM配置生成

**修复前：**
```json
{
  "fields": [
    {
      "name": "title",
      "type": "string",
      "sortable": false,
      "sortOrder": "asc"  // ❌ 不应该存在
    },
    {
      "name": "created_at",
      "type": "datetime",
      "sortable": true,
      "sortOrder": "desc"  // ✅ 正确
    }
  ]
}
```

**修复后：**
```json
{
  "fields": [
    {
      "name": "title",
      "type": "string",
      "sortable": false
      // ✅ 没有sortOrder属性
    },
    {
      "name": "created_at",
      "type": "datetime",
      "sortable": true,
      "sortOrder": "desc"  // ✅ 正确
    }
  ]
}
```

### 2. 字段初始化

**修复前：**
- 所有字段都有 `sortOrder` 属性
- 即使 `sortable: false` 也有排序方向

**修复后：**
- 只有 `sortable: true` 的字段才有 `sortOrder` 属性
- 未启用排序的字段不包含排序信息

### 3. 配置保存

**修复前：**
- 保存时会包含不必要的排序信息
- ORM配置冗余

**修复后：**
- 保存时只包含必要的排序信息
- ORM配置更加精简和准确

## 技术实现要点

### 1. 条件性属性设置

```javascript
// 使用条件判断来决定是否设置属性
if (isSortable) {
  fieldConfig.sortOrder = /* 排序方向逻辑 */;
}
```

### 2. 智能默认值

```javascript
// 根据字段名称智能设置默认排序方向
const defaultSortOrder = (fieldName) => {
  return (fieldName.toLowerCase().includes('id') ||
          fieldName.toLowerCase().includes('time') ||
          fieldName.toLowerCase().includes('date') ||
          fieldName.toLowerCase().includes('created') ||
          fieldName.toLowerCase().includes('updated')) ? 'desc' : 'asc';
};
```

### 3. 一致性保证

- 所有字段初始化位置都使用相同的逻辑
- 保存时的逻辑与初始化逻辑保持一致
- 确保不会出现逻辑不一致的情况

## 测试验证

### 测试步骤

1. **打开可视化配置页面**
2. **检查字段初始状态**：
   - 确认未启用排序的字段没有 `sortOrder` 属性
   - 确认启用排序的字段有正确的 `sortOrder` 属性
3. **测试字段切换**：
   - 将字段的排序状态从禁用切换到启用
   - 确认 `sortOrder` 属性正确添加
   - 将字段的排序状态从启用切换到禁用
   - 确认 `sortOrder` 属性被移除
4. **测试配置保存**：
   - 保存配置并查看生成的ORM配置
   - 确认只有启用排序的字段包含 `sortOrder`
5. **测试配置加载**：
   - 重新加载配置
   - 确认字段状态正确恢复

### 预期结果

✅ **字段初始化正确**：只有启用排序的字段有 `sortOrder` 属性
✅ **状态切换正确**：排序状态变化时 `sortOrder` 属性正确添加/移除
✅ **配置保存正确**：生成的ORM配置不包含不必要的排序信息
✅ **配置加载正确**：重新加载时字段状态正确恢复

## 总结

通过修复字段初始化逻辑中的无条件 `sortOrder` 设置，成功实现了：

1. **精确的属性控制**：只有启用排序的字段才包含排序信息
2. **ORM配置优化**：生成的配置更加精简和准确
3. **逻辑一致性**：所有相关位置都使用相同的条件判断逻辑
4. **用户体验提升**：配置更加直观，避免混淆

现在可视化配置生成的ORM配置将严格按照字段的排序启用状态来决定是否包含排序信息，确保配置的准确性和简洁性。
