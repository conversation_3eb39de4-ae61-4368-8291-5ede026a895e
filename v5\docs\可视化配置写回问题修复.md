# 可视化配置写回问题修复

## 问题描述

用户发现可视化配置写回时出现了两个严重问题：

1. **fields字段为空**：`sqlalchemyModel.fields` 变成了空数组 `[]`
2. **queryMapping重复**：同时存在 `fuzzySearchFields` 和 `fuzzy_search_fields` 等重复字段

## 问题分析

### 问题1：fields字段为空

**根本原因：**
- 可视化配置现在只保存启用的字段（`enabled: true`）
- 原有的ORM配置包含所有字段（包括未启用的）
- 写回时只处理了启用的字段，导致未启用的字段丢失

**问题影响：**
- 数据库字段信息丢失
- 未启用的字段配置被删除
- ORM配置不完整

### 问题2：queryMapping重复字段

**根本原因：**
- 历史配置使用camelCase格式：`fuzzySearchFields`
- 新配置使用snake_case格式：`fuzzy_search_fields`
- 写回时没有清理重复字段，导致两种格式并存

**问题影响：**
- 配置文件冗余
- 可能导致解析混淆
- 维护困难

## 修复方案

### 修复1：保留所有字段信息

**修复策略：**
1. **保留原有字段**：不删除任何原有字段配置
2. **更新匹配字段**：更新在可视化配置中的字段信息
3. **标记启用状态**：正确设置字段的启用状态
4. **添加新字段**：处理新增的字段

**修复前：**
```javascript
// 只处理启用的字段，导致其他字段丢失
const mergedFields = newFields.map((newField: any) => {
  const originalField = originalFields.find((f: any) => f.name === newField.name);
  return originalField ? { ...originalField, ...newField } : newField;
});
```

**修复后：**
```javascript
// 保留所有原有字段，更新匹配的字段信息
const mergedFields = originalFields.map((originalField: any) => {
  const newField = newFields.find((f: any) => f.name === originalField.name);
  if (newField) {
    // 找到匹配的字段，合并属性
    return {
      ...originalField,  // 保留原有属性（如数据库字段信息）
      ...newField,       // 覆盖基本信息（name, type, comment）
      enabled: true      // 在可视化配置中的字段都是启用的
    };
  } else {
    // 没有在可视化配置中的字段，标记为未启用
    return {
      ...originalField,
      enabled: false
    };
  }
});

// 添加新增的字段
const newFieldNames = new Set(originalFields.map((f: any) => f.name));
const additionalFields = newFields.filter((f: any) => !newFieldNames.has(f.name));
config[sqlalchemyModelKey].fields = [...mergedFields, ...additionalFields.map((f: any) => ({ ...f, enabled: true }))];
```

### 修复2：清理重复的queryMapping字段

**修复策略：**
1. **统一格式**：使用snake_case格式作为标准
2. **清理重复**：删除camelCase格式的重复字段
3. **保留其他配置**：保留非重复的其他配置项

**修复前：**
```javascript
// 直接覆盖，可能导致重复字段
config[queryMappingKey] = queryMappingConfig.queryMapping;
```

**修复后：**
```javascript
const newQueryMapping = queryMappingConfig.queryMapping;

// 清理可能存在的重复字段，统一使用snake_case格式
const cleanQueryMapping: any = {};

// 复制新的配置（使用snake_case格式）
if (newQueryMapping.fuzzy_search_fields) {
  cleanQueryMapping.fuzzy_search_fields = newQueryMapping.fuzzy_search_fields;
}
if (newQueryMapping.exact_match_fields) {
  cleanQueryMapping.exact_match_fields = newQueryMapping.exact_match_fields;
}
if (newQueryMapping.range_query_fields) {
  cleanQueryMapping.range_query_fields = newQueryMapping.range_query_fields;
}
if (newQueryMapping.allowed_sort_fields) {
  cleanQueryMapping.allowed_sort_fields = newQueryMapping.allowed_sort_fields;
}
if (newQueryMapping.sort_fields_config) {
  cleanQueryMapping.sort_fields_config = newQueryMapping.sort_fields_config;
}

// 保留其他可能存在的配置（如defaultSort等）
const existingQueryMapping = config[queryMappingKey] || {};
Object.keys(existingQueryMapping).forEach(key => {
  // 跳过重复的camelCase字段和我们已经处理的snake_case字段
  if (!['fuzzySearchFields', 'exactMatchFields', 'rangeQueryFields', 'allowedSortFields', 'sortFieldsConfig',
        'fuzzy_search_fields', 'exact_match_fields', 'range_query_fields', 'allowed_sort_fields', 'sort_fields_config'].includes(key)) {
    cleanQueryMapping[key] = existingQueryMapping[key];
  }
});

config[queryMappingKey] = cleanQueryMapping;
```

## 修复效果

### 修复前的问题配置

```json
{
  "sqlalchemyModel": {
    "className": "ZdyVwGetZyhtList",
    "tableName": "ZDY_VW_GET_ZYHT_LIST",
    "fields": []  // ❌ 字段丢失
  },
  "queryMapping": {
    "fuzzySearchFields": ["F_CODE", "F_CREATOR_NAME", "F_NAME"],     // ❌ 重复
    "exactMatchFields": ["F_NAME"],                                   // ❌ 重复
    "fuzzy_search_fields": ["F_CODE", "F_CREATOR_NAME", "F_NAME"],   // ❌ 重复
    "exact_match_fields": ["F_NAME"]                                  // ❌ 重复
  }
}
```

### 修复后的正确配置

```json
{
  "sqlalchemyModel": {
    "className": "ZdyVwGetZyhtList",
    "tableName": "ZDY_VW_GET_ZYHT_LIST",
    "fields": [  // ✅ 保留所有字段
      {
        "name": "F_CODE",
        "type": "string",
        "comment": "编码",
        "enabled": true,     // ✅ 启用状态正确
        "nullable": true,    // ✅ 保留数据库属性
        "length": 50
      },
      {
        "name": "F_OTHER_FIELD",
        "type": "string",
        "comment": "其他字段",
        "enabled": false,    // ✅ 未启用字段保留
        "nullable": true,
        "length": 100
      }
    ]
  },
  "queryMapping": {
    // ✅ 只保留snake_case格式，无重复
    "fuzzy_search_fields": ["F_CODE", "F_CREATOR_NAME", "F_NAME"],
    "exact_match_fields": ["F_NAME"],
    "range_query_fields": ["F_NAME"],
    "allowed_sort_fields": ["F_CODE", "F_CREATOR_NAME"],
    "sort_fields_config": {
      "F_CODE": "asc",
      "F_CREATOR_NAME": "asc"
    },
    // ✅ 保留其他非重复配置
    "defaultSort": "F_CODE",
    "defaultOrder": "desc"
  }
}
```

## 技术实现要点

### 1. 字段保留策略

```javascript
// 遍历原有字段，而不是新字段
const mergedFields = originalFields.map((originalField: any) => {
  // 查找对应的新字段配置
  const newField = newFields.find((f: any) => f.name === originalField.name);
  // 根据是否找到来决定启用状态
});
```

### 2. 重复字段清理

```javascript
// 明确列出需要清理的重复字段
const duplicateFields = [
  'fuzzySearchFields', 'exactMatchFields', 'rangeQueryFields', 
  'allowedSortFields', 'sortFieldsConfig',
  'fuzzy_search_fields', 'exact_match_fields', 'range_query_fields', 
  'allowed_sort_fields', 'sort_fields_config'
];
```

### 3. 配置合并逻辑

```javascript
// 先处理标准字段，再保留其他配置
Object.keys(existingQueryMapping).forEach(key => {
  if (!duplicateFields.includes(key)) {
    cleanQueryMapping[key] = existingQueryMapping[key];
  }
});
```

## 测试验证

### 测试场景

1. **字段保留测试**：
   - 原有配置包含10个字段
   - 可视化配置只启用5个字段
   - 验证写回后仍有10个字段，启用状态正确

2. **重复字段清理测试**：
   - 原有配置包含camelCase和snake_case重复字段
   - 写回后只保留snake_case格式
   - 验证无重复字段

3. **新字段添加测试**：
   - 可视化配置包含新字段
   - 验证新字段正确添加到配置中

4. **其他配置保留测试**：
   - 原有配置包含defaultSort等其他配置
   - 验证这些配置在写回后保留

### 预期结果

✅ **字段完整性**：所有原有字段都保留，启用状态正确
✅ **无重复字段**：queryMapping中无重复的camelCase和snake_case字段
✅ **新字段支持**：新增字段正确添加
✅ **配置保留**：其他非重复配置项正确保留

## 总结

通过修复字段保留逻辑和清理重复字段，成功解决了：

1. **数据完整性**：确保所有字段信息不丢失
2. **配置一致性**：统一使用snake_case格式，消除重复
3. **功能完整性**：保持所有原有功能和配置
4. **向后兼容**：正确处理历史配置格式

现在可视化配置写回功能能够正确保留所有字段信息，同时消除配置重复问题。
