# PowerShell脚本：批量注释掉console.log，保留console.error和console.warn

param(
    [string]$FilePath
)

if (-not $FilePath) {
    Write-Host "用法: .\clean-console-logs.ps1 -FilePath 'path/to/file.vue'"
    exit 1
}

if (-not (Test-Path $FilePath)) {
    Write-Host "文件不存在: $FilePath"
    exit 1
}

Write-Host "开始清理文件: $FilePath"

# 读取文件内容
$content = Get-Content $FilePath -Raw

# 备份原文件
$backupPath = $FilePath + ".backup"
Copy-Item $FilePath $backupPath
Write-Host "已备份原文件到: $backupPath"

# 替换规则：
# 1. console.log(...) -> // console.log(...)
# 2. 保留 console.error 和 console.warn
# 3. 处理多行的console.log

# 处理单行的console.log
$content = $content -replace '(\s+)console\.log\(', '$1// console.log('

# 处理已经被注释的console.log（避免重复注释）
$content = $content -replace '(\s+)// // console\.log\(', '$1// console.log('

# 写回文件
Set-Content -Path $FilePath -Value $content -NoNewline

Write-Host "✅ 清理完成！"
Write-Host "📊 统计信息："

# 统计console.log数量
$logCount = ([regex]::Matches($content, '// console\.log\(')).Count
$errorCount = ([regex]::Matches($content, 'console\.error\(')).Count
$warnCount = ([regex]::Matches($content, 'console\.warn\(')).Count

Write-Host "  - 已注释的console.log: $logCount"
Write-Host "  - 保留的console.error: $errorCount" 
Write-Host "  - 保留的console.warn: $warnCount"
