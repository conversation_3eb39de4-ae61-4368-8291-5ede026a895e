<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态API测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 动态API测试工具</h1>
        <p>用于测试ORM配置、参数配置和动态API功能</p>

        <!-- 测试1：基本GET请求 -->
        <div class="test-section">
            <h3>📋 测试1：基本GET请求</h3>
            <div class="form-group">
                <label>接口路径：</label>
                <input type="text" id="getPath" value="/api/dynamic/v1/project/ht1" placeholder="例如：/api/dynamic/v1/project/ht1">
            </div>
            <div class="form-group">
                <label>查询参数（JSON格式）：</label>
                <textarea id="getParams" placeholder='{"page": 1, "size": 10}'>{
  "page": 1,
  "size": 10
}</textarea>
            </div>
            <button onclick="testGet()">发送GET请求</button>
            <div id="getResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试2：模糊查询 -->
        <div class="test-section">
            <h3>🔍 测试2：模糊查询（keyword参数）</h3>
            <div class="form-group">
                <label>接口路径：</label>
                <input type="text" id="searchPath" value="/api/dynamic/v1/project/ht1" placeholder="例如：/api/dynamic/v1/project/ht1">
            </div>
            <div class="form-group">
                <label>关键词：</label>
                <input type="text" id="keyword" value="测试" placeholder="输入搜索关键词">
            </div>
            <button onclick="testSearch()">测试模糊查询</button>
            <div id="searchResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试3：精确查询 -->
        <div class="test-section">
            <h3>🎯 测试3：精确查询（字段名=值）</h3>
            <div class="form-group">
                <label>接口路径：</label>
                <input type="text" id="exactPath" value="/api/dynamic/v1/project/ht1" placeholder="例如：/api/dynamic/v1/project/ht1">
            </div>
            <div class="form-group">
                <label>精确查询参数（JSON格式）：</label>
                <textarea id="exactParams" placeholder='{"status": "active", "type": "admin"}'>{
  "F_STATUS": "1",
  "F_TYPE": "test"
}</textarea>
            </div>
            <button onclick="testExact()">测试精确查询</button>
            <div id="exactResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试4：排序查询 -->
        <div class="test-section">
            <h3>📊 测试4：排序查询</h3>
            <div class="form-group">
                <label>接口路径：</label>
                <input type="text" id="sortPath" value="/api/dynamic/v1/project/ht1" placeholder="例如：/api/dynamic/v1/project/ht1">
            </div>
            <div class="form-group">
                <label>排序字段：</label>
                <input type="text" id="sortField" value="F_CODE" placeholder="例如：F_CODE">
            </div>
            <div class="form-group">
                <label>排序方向：</label>
                <select id="sortOrder">
                    <option value="asc">升序 (asc)</option>
                    <option value="desc">降序 (desc)</option>
                </select>
            </div>
            <button onclick="testSort()">测试排序查询</button>
            <div id="sortResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试5：组合查询 -->
        <div class="test-section">
            <h3>🔄 测试5：组合查询（模糊+精确+排序）</h3>
            <div class="form-group">
                <label>接口路径：</label>
                <input type="text" id="comboPath" value="/api/dynamic/v1/project/ht1" placeholder="例如：/api/dynamic/v1/project/ht1">
            </div>
            <div class="form-group">
                <label>组合查询参数（JSON格式）：</label>
                <textarea id="comboParams" placeholder='完整的查询参数'>{
  "keyword": "测试",
  "F_STATUS": "1",
  "sort": "F_CODE",
  "order": "desc",
  "page": 1,
  "size": 5
}</textarea>
            </div>
            <button onclick="testCombo()">测试组合查询</button>
            <div id="comboResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 通用请求函数
        async function makeRequest(url, params = {}) {
            try {
                const queryString = new URLSearchParams(params).toString();
                const fullUrl = queryString ? `${url}?${queryString}` : url;
                
                console.log('请求URL:', fullUrl);
                
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 显示结果
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = `✅ 请求成功 (${result.status})\n\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ 请求失败\n\n${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        // 显示加载状态
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = '⏳ 请求中...';
        }

        // 测试基本GET请求
        async function testGet() {
            const path = document.getElementById('getPath').value;
            const paramsText = document.getElementById('getParams').value;
            
            showLoading('getResult');
            
            try {
                const params = paramsText ? JSON.parse(paramsText) : {};
                const result = await makeRequest(`http://127.0.0.1:8000${path}`, params);
                showResult('getResult', result);
            } catch (error) {
                showResult('getResult', { success: false, error: '参数JSON格式错误: ' + error.message });
            }
        }

        // 测试模糊查询
        async function testSearch() {
            const path = document.getElementById('searchPath').value;
            const keyword = document.getElementById('keyword').value;
            
            showLoading('searchResult');
            
            const params = {
                keyword: keyword,
                page: 1,
                size: 10
            };
            
            const result = await makeRequest(`http://127.0.0.1:8000${path}`, params);
            showResult('searchResult', result);
        }

        // 测试精确查询
        async function testExact() {
            const path = document.getElementById('exactPath').value;
            const paramsText = document.getElementById('exactParams').value;
            
            showLoading('exactResult');
            
            try {
                const params = JSON.parse(paramsText);
                params.page = 1;
                params.size = 10;
                
                const result = await makeRequest(`http://127.0.0.1:8000${path}`, params);
                showResult('exactResult', result);
            } catch (error) {
                showResult('exactResult', { success: false, error: '参数JSON格式错误: ' + error.message });
            }
        }

        // 测试排序查询
        async function testSort() {
            const path = document.getElementById('sortPath').value;
            const sortField = document.getElementById('sortField').value;
            const sortOrder = document.getElementById('sortOrder').value;
            
            showLoading('sortResult');
            
            const params = {
                sort: sortField,
                order: sortOrder,
                page: 1,
                size: 10
            };
            
            const result = await makeRequest(`http://127.0.0.1:8000${path}`, params);
            showResult('sortResult', result);
        }

        // 测试组合查询
        async function testCombo() {
            const path = document.getElementById('comboPath').value;
            const paramsText = document.getElementById('comboParams').value;
            
            showLoading('comboResult');
            
            try {
                const params = JSON.parse(paramsText);
                const result = await makeRequest(`http://127.0.0.1:8000${path}`, params);
                showResult('comboResult', result);
            } catch (error) {
                showResult('comboResult', { success: false, error: '参数JSON格式错误: ' + error.message });
            }
        }
    </script>
</body>
</html>
