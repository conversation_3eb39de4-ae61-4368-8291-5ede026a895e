"""
接口配置数据模式定义
"""

from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class HttpMethod(str, Enum):
    """HTTP方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


class TestStatus(str, Enum):
    """测试状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"


class InterfaceConfigBase(BaseModel):
    """接口配置基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="接口名称")
    path: str = Field(..., min_length=1, max_length=200, description="接口路径")
    method: HttpMethod = Field(..., description="HTTP方法")
    description: Optional[str] = Field(None, description="接口描述")
    group_id: int = Field(..., description="所属分组ID")
    datasource_id: int = Field(..., description="数据源ID")
    table_name: str = Field(..., min_length=1, max_length=100, description="数据表名")
    table_type: str = Field('table', description="数据表类型(table/view/procedure)")
    is_enabled: bool = Field(True, description="是否启用")
    is_public: bool = Field(False, description="是否公开(无需认证)")
    query_fields: Optional[List[str]] = Field(None, description="可查询字段")
    required_fields: Optional[List[str]] = Field(None, description="必填字段")
    response_fields: Optional[List[str]] = Field(None, description="响应字段")

    # ORM模型配置 - 使用snake_case命名规范
    orm_model_config: Optional[dict] = Field(None, description="ORM模型配置")
    orm_model_name: Optional[str] = Field(None, max_length=100, description="ORM模型名称")
    orm_relationships: Optional[dict] = Field(None, description="ORM关联关系配置")

    # 参数配置 - 使用snake_case命名规范
    parameter_config: Optional[dict] = Field(None, description="参数配置")
    visual_config: Optional[dict] = Field(None, description="可视化配置")
    validation_rules: Optional[dict] = Field(None, description="验证规则配置")

    cache_duration: int = Field(300, ge=0, le=3600, description="缓存时长(秒)")
    rate_limit: int = Field(100, ge=1, le=10000, description="速率限制(次/分钟)")
    
    @validator('path')
    def validate_path(cls, v):
        """验证接口路径格式"""
        if not v.startswith('/'):
            raise ValueError('接口路径必须以/开头')
        return v


class InterfaceConfigCreate(InterfaceConfigBase):
    """接口配置创建模式"""
    tags: Optional[List[int]] = Field(None, description="标签ID列表")
    created_by: Optional[str] = Field(None, max_length=50, description="创建人")


class InterfaceConfigUpdate(BaseModel):
    """接口配置更新模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="接口名称")
    path: Optional[str] = Field(None, min_length=1, max_length=200, description="接口路径")
    method: Optional[HttpMethod] = Field(None, description="HTTP方法")
    description: Optional[str] = Field(None, description="接口描述")
    group_id: Optional[int] = Field(None, description="所属分组ID")
    datasource_id: Optional[int] = Field(None, description="数据源ID")
    table_name: Optional[str] = Field(None, min_length=1, max_length=100, description="数据表名")
    table_type: Optional[str] = Field(None, description="数据表类型(table/view/procedure)")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    is_public: Optional[bool] = Field(None, description="是否公开(无需认证)")
    query_fields: Optional[List[str]] = Field(None, description="可查询字段")
    required_fields: Optional[List[str]] = Field(None, description="必填字段")
    response_fields: Optional[List[str]] = Field(None, description="响应字段")

    # ORM模型配置
    orm_model_config: Optional[dict] = Field(None, description="ORM模型配置")
    orm_model_name: Optional[str] = Field(None, max_length=100, description="ORM模型名称")
    orm_relationships: Optional[dict] = Field(None, description="ORM关联关系配置")

    # 参数配置
    parameter_config: Optional[dict] = Field(None, description="参数配置")
    visual_config: Optional[dict] = Field(None, description="可视化配置")
    validation_rules: Optional[dict] = Field(None, description="验证规则配置")

    cache_duration: Optional[int] = Field(None, ge=0, le=3600, description="缓存时长(秒)")
    rate_limit: Optional[int] = Field(None, ge=1, le=10000, description="速率限制(次/分钟)")
    tags: Optional[List[int]] = Field(None, description="标签ID列表")
    
    @validator('path')
    def validate_path(cls, v):
        """验证接口路径格式"""
        if v is not None and not v.startswith('/'):
            raise ValueError('接口路径必须以/开头')
        return v


class InterfaceConfigResponse(InterfaceConfigBase):
    """接口配置响应模式"""
    id: int
    group_name: Optional[str] = Field(None, description="所属分组名称")
    datasource_name: Optional[str] = Field(None, description="数据源名称")
    tags: Optional[List[int]] = Field(None, description="标签ID列表")
    tag_names: Optional[List[str]] = Field(None, description="标签名称列表")
    last_test_at: Optional[datetime] = Field(None, description="最后测试时间")
    test_status: Optional[TestStatus] = Field(None, description="测试状态")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    created_by: Optional[str] = Field(None, description="创建人")
    
    class Config:
        from_attributes = True
        populate_by_name = True  # 允许使用字段名和别名
        allow_population_by_field_name = True  # 兼容旧版本
        by_alias = False  # 序列化时使用字段名而不是别名


class InterfaceConfigListResponse(BaseModel):
    """接口配置列表响应模式"""
    items: list[InterfaceConfigResponse]
    total: int
    page: int
    size: int
    pages: int
