# 常用参数 Placeholder 修复说明

## 问题描述

用户反馈：常用参数选择之后，在参数表格的"默认值"字段中显示的是实际字符串内容，而不是placeholder提示。

## 问题分析

### 原始问题
1. **空字符串默认值**：常用参数配置中设置了 `defaultValue: ''`（空字符串）
2. **Placeholder不显示**：当输入框的value为空字符串时，placeholder不会显示
3. **固定placeholder**：表格中使用固定的 `placeholder="默认值"`，不够具体

### 根本原因
- Element Plus的 `el-input` 组件只有在 `v-model` 的值为 `undefined`、`null` 或真正的空值时才会显示placeholder
- 空字符串 `''` 被认为是有值，所以不会显示placeholder

## 修复方案

### 1. 修改常用参数配置

**修改前：**
```javascript
Authorization: {
  name: 'Authorization',
  type: 'header',
  dataType: 'string',
  required: true,
  defaultValue: '',  // 空字符串
  description: '认证令牌（如：Bearer token）',
  placeholder: 'Bearer your-token-here'  // 这个placeholder不会生效
}
```

**修改后：**
```javascript
Authorization: {
  name: 'Authorization',
  type: 'header',
  dataType: 'string',
  required: true,
  defaultValue: undefined,  // undefined以显示placeholder
  description: '认证令牌（如：Bearer token）'
}
```

### 2. 增加智能Placeholder函数

添加了 `getDefaultValuePlaceholder` 函数，根据参数类型和名称提供具体的placeholder：

```javascript
const getDefaultValuePlaceholder = (row: any) => {
  if (row.type === 'header') {
    switch (row.name) {
      case 'Authorization':
        return 'Bearer your-token-here';
      case 'X-API-Key':
        return '请输入API密钥';
      case 'X-Request-ID':
        return '请输入请求ID';
      case 'User-Agent':
        return '请输入User-Agent';
      case 'Content-Type':
        return '已设置默认值';
      case 'Accept':
        return '已设置默认值';
      default:
        return '请输入默认值';
    }
  } else if (row.type === 'query') {
    switch (row.name) {
      case 'keyword':
        return '请输入搜索关键词';
      default:
        return '请输入默认值';
    }
  } else if (row.type === 'path') {
    return '路径参数无需默认值';
  } else {
    return '请输入默认值';
  }
};
```

### 3. 更新表格模板

**修改前：**
```html
<el-input v-model="row.defaultValue" placeholder="默认值" size="small" />
```

**修改后：**
```html
<el-input 
  v-model="row.defaultValue" 
  :placeholder="getDefaultValuePlaceholder(row)" 
  size="small" 
/>
```

## 修复结果

### 现在的行为
1. **选择常用参数**：用户从下拉菜单选择参数（如Authorization）
2. **添加到表格**：参数被添加到参数列表中
3. **显示placeholder**：在"默认值"列中显示具体的placeholder提示
4. **智能提示**：不同类型的参数显示不同的placeholder

### 具体示例

| 参数名 | 类型 | 默认值显示 |
|--------|------|------------|
| Authorization | header | `Bearer your-token-here` |
| X-API-Key | header | `请输入API密钥` |
| keyword | query | `请输入搜索关键词` |
| Content-Type | header | `已设置默认值` (因为有实际默认值) |
| Accept | header | `已设置默认值` (因为有实际默认值) |

## 技术要点

1. **undefined vs 空字符串**：使用 `undefined` 而不是 `''` 来确保placeholder显示
2. **有意义的默认值保留**：`Content-Type` 和 `Accept` 保留了 `'application/json'` 默认值
3. **动态placeholder**：根据参数类型和名称提供具体的提示文本
4. **用户体验优化**：让用户清楚知道每个字段应该输入什么内容

## 测试验证

用户可以通过以下步骤验证修复效果：
1. 打开接口配置表单的"参数配置"页签
2. 点击"常用参数"下拉菜单
3. 选择任意参数（如Authorization）
4. 查看参数表格中"默认值"列是否显示正确的placeholder
5. 输入内容后再清空，确认placeholder重新显示
