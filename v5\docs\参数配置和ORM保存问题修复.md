# 参数配置和ORM保存问题修复

## 问题描述

用户反馈了两个问题：
1. **参数配置placeholder问题**：选择keyword参数后，在默认值字段显示了描述内容而不是placeholder
2. **ORM配置保存问题**：修改ORM字段后保存失败，可能与新增的排序方向字段有关

## 问题分析

### 问题1：keyword参数的placeholder显示

**原始配置：**
```javascript
keyword: {
  name: 'keyword',
  type: 'query',
  dataType: 'string',
  required: false,
  defaultValue: undefined,  // undefined以显示placeholder
  description: '搜索关键词（配合字段模糊查询使用）'  // 描述过长
}
```

**问题原因：**
- 描述文本过长，在表格中显示不美观
- 用户可能混淆了"默认值"字段和"描述"字段的显示

### 问题2：ORM配置保存失败

**原始逻辑：**
```javascript
// 直接替换整个字段数组
config[sqlalchemyModelKey].fields = enabledFields;
```

**问题原因：**
1. **属性丢失**：直接替换字段数组会丢失原有的字段属性
2. **新属性冲突**：新增的`sortOrder`属性可能与原有结构不兼容
3. **合并不当**：没有正确合并原有字段属性和新的可视化配置

## 修复方案

### 修复1：优化keyword参数配置

**修改后：**
```javascript
keyword: {
  name: 'keyword',
  type: 'query',
  dataType: 'string',
  required: false,
  defaultValue: '',  // 空字符串，让placeholder显示
  description: '搜索关键词'  // 简化描述
}
```

**改进点：**
- 简化描述文本，去除冗余信息
- 使用空字符串作为默认值，确保placeholder正确显示
- 保持功能说明的简洁性

### 修复2：改进ORM字段合并逻辑

**新的合并策略：**
```javascript
// 更新字段配置，保留原有属性并合并新属性
if (result.visualFields && Array.isArray(result.visualFields)) {
  const enabledFields = result.visualFields.filter((field: any) => field.enabled);
  
  // 如果原来有字段配置，则合并属性；否则直接使用新字段
  if (config[sqlalchemyModelKey].fields && Array.isArray(config[sqlalchemyModelKey].fields)) {
    const originalFields = config[sqlalchemyModelKey].fields;
    const mergedFields = enabledFields.map((newField: any) => {
      // 查找原有字段
      const originalField = originalFields.find((f: any) => f.name === newField.name);
      if (originalField) {
        // 合并原有字段和新字段属性
        return {
          ...originalField,  // 保留原有属性
          ...newField,       // 覆盖新属性
          // 确保关键属性正确
          enabled: newField.enabled,
          searchable: newField.searchable,
          filterable: newField.filterable,
          rangeable: newField.rangeable,
          sortable: newField.sortable,
          sortOrder: newField.sortOrder || originalField.sortOrder || 'desc'
        };
      } else {
        // 新字段，直接使用
        return newField;
      }
    });
    config[sqlalchemyModelKey].fields = mergedFields;
  } else {
    // 没有原有字段，直接使用新字段
    config[sqlalchemyModelKey].fields = enabledFields;
  }
}
```

**改进点：**
1. **属性保留**：保留原有字段的所有属性（如sqlalchemy_type、nullable等）
2. **智能合并**：新属性覆盖旧属性，但保留重要的原有信息
3. **向后兼容**：支持没有原有字段配置的情况
4. **排序方向处理**：正确处理新增的sortOrder属性

## 修复效果

### 问题1修复效果

**现在的行为：**
1. 选择keyword参数后，"描述"列显示"搜索关键词"
2. "默认值"列显示placeholder："请输入搜索关键词"
3. 用户界面更加清晰，不会产生混淆

### 问题2修复效果

**现在的行为：**
1. **保留原有属性**：字段的类型、长度、注释等信息不会丢失
2. **正确更新配置**：可视化配置的修改能够正确应用到ORM配置
3. **排序方向保存**：新增的排序方向设置能够正确保存
4. **向后兼容**：兼容现有的ORM配置结构

## 测试验证

### 测试场景1：参数配置

1. 打开接口配置的"参数配置"页签
2. 点击"常用参数" → 选择"keyword"
3. 验证：
   - "参数名"列显示：keyword
   - "描述"列显示：搜索关键词
   - "默认值"列显示placeholder：请输入搜索关键词

### 测试场景2：ORM配置保存

1. 打开接口配置，确保有ORM配置
2. 点击"可视化配置"按钮
3. 修改字段的排序方向设置
4. 点击"保存配置"
5. 验证：
   - 配置保存成功提示
   - ORM配置中包含新的排序方向信息
   - 原有字段属性（如类型、长度等）保持不变

### 测试场景3：完整流程

1. 创建新接口配置
2. 设置基础信息和数据表
3. 生成ORM配置
4. 使用可视化配置修改字段设置
5. 添加参数配置
6. 保存整个接口配置
7. 验证所有配置都正确保存

## 技术要点

### 1. 对象合并策略

使用展开运算符进行浅合并，确保新属性覆盖旧属性：
```javascript
return {
  ...originalField,  // 保留原有属性
  ...newField,       // 覆盖新属性
  // 显式设置关键属性
}
```

### 2. 数组查找和映射

使用`find`方法查找对应字段，使用`map`方法生成新数组：
```javascript
const originalField = originalFields.find((f: any) => f.name === newField.name);
```

### 3. 默认值处理

为新属性提供合理的默认值：
```javascript
sortOrder: newField.sortOrder || originalField.sortOrder || 'desc'
```

### 4. 错误处理

保持原有的错误处理逻辑，确保异常情况下的用户体验。

## 后续优化建议

1. **类型安全**：为字段对象定义TypeScript接口
2. **单元测试**：为合并逻辑编写单元测试
3. **性能优化**：对于大量字段的情况，考虑优化查找算法
4. **用户反馈**：收集用户对新功能的使用反馈
