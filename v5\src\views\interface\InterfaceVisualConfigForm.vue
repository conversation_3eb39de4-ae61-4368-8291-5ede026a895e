<template>
  <div class="visual-config-container">
    <!-- 调试信息 -->
    <div style="background: red; color: white; padding: 10px; margin-bottom: 10px;">
      🔧 可视化配置已加载 - 字段数量: {{ visualFields.length }}
    </div>

    <!-- 内容区域 - 使用flex布局 -->
    <div class="content-area">
      <!-- 顶部固定区域 -->
      <div class="header-section">
        <div class="config-tip">
          <el-icon><InfoFilled /></el-icon>
          配置哪些字段可以被搜索、过滤和排序，这将影响接口的查询能力
        </div>

        <!-- 数据源信息显示和操作按钮 -->
        <div class="datasource-info-header">
          <div class="datasource-info">
            <el-tag type="info" size="small">
              数据源：{{ getCurrentDatasourceName() }}
            </el-tag>
            <el-tag type="success" size="small" style="margin-left: 8px;">
              数据表：{{ formData?.tableName || formData?.table_name || '未选择' }}
            </el-tag>
          </div>
          <div class="datasource-actions">
            <el-button size="small" @click="handleLoadFromTable" :loading="loadingFromTable">
              <el-icon><Refresh /></el-icon>
              从数据表加载
            </el-button>
          </div>
        </div>
      </div>

      <!-- 字段配置表格 - 占据剩余空间 -->
      <div class="table-container">

      <div class="table-wrapper custom-scrollbar">
        <el-table
          :data="visualFields"
          border
          style="width: 100%; min-width: 1200px;"
          :height="tableHeight"
          :show-overflow-tooltip="true"
        >
        <el-table-column prop="name" label="字段名" width="120">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="字段类型" width="120">
          <template #default="{ row }">
            <el-select
              v-if="row.source === 'custom'"
              v-model="row.type"
              size="small"
              style="width: 100%"
            >
              <el-option label="String(100)" value="String(100)" />
              <el-option label="Integer" value="Integer" />
              <el-option label="DateTime" value="DateTime" />
              <el-option label="Boolean" value="Boolean" />
              <el-option label="Text" value="Text" />
            </el-select>
            <span v-else>{{ row.type }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="comment" label="字段说明" min-width="150">
          <template #default="{ row }">
            <el-input
              v-model="row.comment"
              size="small"
              placeholder="字段说明"
            />
          </template>
        </el-table-column>



        <el-table-column prop="enabled" label="启用" width="60" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.enabled" size="small" />
          </template>
        </el-table-column>

        <el-table-column prop="searchable" label="模糊搜索" width="80" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.searchable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="filterable" label="精确过滤" width="80" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.filterable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="rangeable" label="范围查询" width="80" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.rangeable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="sortable" label="排序" width="60" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.sortable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="sortOrder" label="排序方向" width="100" align="center">
          <template #default="{ row }">
            <el-select
              v-model="row.sortOrder"
              size="small"
              style="width: 80px"
              :disabled="!row.enabled || !row.sortable"
              placeholder="方向"
            >
              <el-option label="升序" value="asc" />
              <el-option label="降序" value="desc" />
            </el-select>
          </template>
        </el-table-column>


        </el-table>
      </div>
    </div>
    </div>

    <!-- 底部按钮已抽象到MainIndex中统一管理 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { InfoFilled, Refresh } from '@element-plus/icons-vue';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore.ts';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import dataSourceService from '@/services/datasource.service';


// 抽屉状态管理
const globalDrawerStore = useGlobalDrawerStore();
const drawerMessenger = useGlobalDrawerMessenger();

// 响应式数据
const visualFields = ref<any[]>([]);
const loadingFromTable = ref(false);
const dataSources = ref<any[]>([]);
const tableHeight = ref<number>(400);

// 计算属性 - 使用第二层抽屉的数据
const formData = computed(() => {
  const props = globalDrawerStore.secondProps;
  console.log('🔍 计算属性 formData - secondProps:', props);
  return props?.editData || props;
});

// 加载数据源列表
const loadDataSources = async () => {
  try {
    const response = await dataSourceService.getDataSources(1, 100); // 获取前100个数据源
    dataSources.value = response?.items || [];
  } catch (error) {
    console.error('加载数据源列表失败:', error);
    dataSources.value = [];
  }
};

// 获取数据源名称
const getCurrentDatasourceName = () => {
  const datasourceId = formData.value?.datasourceId || formData.value?.datasource_id;
  if (!datasourceId) {
    return '未选择数据源';
  }

  const dataSource = dataSources.value.find(ds => ds.id === datasourceId);
  return dataSource ? dataSource.name : `数据源ID: ${datasourceId}`;
};

// 从ORM配置和数据表结构加载字段
const loadFieldsFromOrmConfig = async () => {
  visualFields.value = [];

  console.log('🔍 loadFieldsFromOrmConfig 开始');
  console.log('🔍 globalDrawerStore.secondProps:', globalDrawerStore.secondProps);
  console.log('🔍 formData.value:', formData.value);

  if (!formData.value) {
    console.log('❌ formData.value 为空');
    return;
  }

  // 尝试多种可能的字段名
  const ormConfig = formData.value?.ormModelConfig || formData.value?.orm_model_config || formData.value?.orm_config;
  console.log('🔍 ormConfig:', ormConfig);
  console.log('🔍 ormConfig 类型:', typeof ormConfig);
  console.log('🔍 可用的ORM字段:', Object.keys(formData.value || {}).filter(k => k.toLowerCase().includes('orm')));

  // 第一步：从ORM配置中获取已配置的字段
  let ormFields = [];
  let queryMapping = null;

  // 第二步：从数据表结构获取完整字段列表
  let tableFields = [];
  try {
    if (formData.value.datasourceId && formData.value.tableName) {
      console.log('🔍 开始获取数据表结构');
      tableFields = await getTableStructure(formData.value.datasourceId, formData.value.tableName, formData.value.tableType);
      console.log('🔍 数据表字段：', tableFields);
    }
  } catch (error) {
    console.warn('⚠️ 获取数据表结构失败：', error);
  }

  // 第三步：解析ORM配置中的字段
  if (ormConfig) {
    try {
      const config = typeof ormConfig === 'string' ? JSON.parse(ormConfig) : ormConfig;
      console.log('🔍 解析后的ORM配置：', config);

      // 支持多种字段名格式（camelCase 和 snake_case）
      const sqlalchemyModel = config.sqlalchemyModel || config.sqlalchemy_model;
      queryMapping = config.queryMapping || config.query_mapping;

      if (sqlalchemyModel && sqlalchemyModel.fields) {
        ormFields = sqlalchemyModel.fields;
        console.log('🔍 ORM配置中的字段：', ormFields);
      }
    } catch (error) {
      console.warn('⚠️ ORM配置解析失败:', error);
    }
  }

  // 第四步：合并字段列表
  console.log('🔍 开始合并字段:', {
    ormFieldsCount: ormFields.length,
    tableFieldsCount: tableFields.length,
    ormFields: ormFields.map((f: { name: any; }) => f.name),
    tableFields: tableFields.map((f: { name: any; }) => f.name)
  });

  const mergedFields = mergeFieldsFromOrmAndTable(ormFields, tableFields, queryMapping);
  visualFields.value = mergedFields;

  console.log('✅ 最终合并的字段列表:', {
    totalCount: visualFields.value.length,
    enabledCount: visualFields.value.filter(f => f.enabled).length,
    fields: visualFields.value.map(f => ({ name: f.name, enabled: f.enabled, status: f.status }))
  });
};

// 获取数据表结构
const getTableStructure = async (datasourceId: number, tableName: string, tableType: string) => {
  try {
    console.log('🔍 调用表结构API:', { datasourceId, tableName, tableType });

    const result = await dataSourceService.getTableStructure(datasourceId, tableName, tableType);
    console.log('🔍 表结构API响应:', result);

    if (result && result.columns) {
      return result.columns;
    }
    return [];
  } catch (error) {
    console.error('❌ 获取表结构失败:', error);
    return [];
  }
};

// 合并ORM字段和数据表字段
const mergeFieldsFromOrmAndTable = (ormFields: any[], tableFields: any[], queryMapping: any) => {
  const fieldMap = new Map();

  // 第一步：添加数据表中的所有字段（默认禁用）
  tableFields.forEach(tableField => {
    fieldMap.set(tableField.name, {
      name: tableField.name,
      originalName: tableField.original_name || tableField.name,
      type: tableField.type,
      sqlalchemyType: tableField.sqlalchemy_type,
      comment: tableField.comment || '',
      nullable: tableField.nullable,
      primaryKey: tableField.primary_key || false,
      defaultValue: tableField.default,
      length: tableField.length,
      precision: tableField.precision,
      scale: tableField.scale,
      // 默认状态：从表结构来的字段默认禁用
      enabled: false,
      searchable: false,
      filterable: false,
      rangeable: false,
      sortable: false,
      sortOrder: 'desc', // 默认降序
      source: 'table_structure',
      status: 'from_table'
    });
  });

  // 第二步：覆盖ORM配置中的字段状态
  ormFields.forEach(ormField => {
    if (fieldMap.has(ormField.name)) {
      // 字段在表结构中存在，更新状态
      const existingField = fieldMap.get(ormField.name);
      fieldMap.set(ormField.name, {
        ...existingField,
        ...ormField,
        // 从查询映射中获取配置状态
        searchable: queryMapping?.fuzzySearchFields?.includes(ormField.name) ||
                   queryMapping?.fuzzy_search_fields?.includes(ormField.name) || false,
        filterable: queryMapping?.exactMatchFields?.includes(ormField.name) ||
                   queryMapping?.exact_match_fields?.includes(ormField.name) || false,
        rangeable: queryMapping?.rangeQueryFields?.includes(ormField.name) ||
                  queryMapping?.range_query_fields?.includes(ormField.name) || false,
        sortable: queryMapping?.allowedSortFields?.includes(ormField.name) ||
                 queryMapping?.allowed_sort_fields?.includes(ormField.name) || false,
        sortOrder: ormField.sortOrder || ormField.sort_order ||
                  (ormField.name.toLowerCase().includes('id') ||
                   ormField.name.toLowerCase().includes('time') ||
                   ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc'),
        enabled: ormField.enabled !== undefined ? ormField.enabled : true,
        source: ormField.source || 'orm_config',
        status: 'in_orm'
      });
    } else {
      // 字段在表结构中不存在（可能已删除），但保留在ORM中
      fieldMap.set(ormField.name, {
        ...ormField,
        searchable: queryMapping?.fuzzySearchFields?.includes(ormField.name) ||
                   queryMapping?.fuzzy_search_fields?.includes(ormField.name) || false,
        filterable: queryMapping?.exactMatchFields?.includes(ormField.name) ||
                   queryMapping?.exact_match_fields?.includes(ormField.name) || false,
        rangeable: queryMapping?.rangeQueryFields?.includes(ormField.name) ||
                  queryMapping?.range_query_fields?.includes(ormField.name) || false,
        sortable: queryMapping?.allowedSortFields?.includes(ormField.name) ||
                 queryMapping?.allowed_sort_fields?.includes(ormField.name) || false,
        sortOrder: ormField.sortOrder || ormField.sort_order ||
                  (ormField.name.toLowerCase().includes('id') ||
                   ormField.name.toLowerCase().includes('time') ||
                   ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc'),
        enabled: ormField.enabled !== undefined ? ormField.enabled : true,
        source: ormField.source || 'orm_config',
        status: 'deleted_from_table' // 标记为已从表中删除
      });
    }
  });

  return Array.from(fieldMap.values());
};

// 获取字段状态类型（用于标签颜色）

// 获取字段状态文本

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer(true);  // 关闭第二层抽屉
};

// 保存配置
const handleSave = () => {
  try {
    // 验证必填字段
    for (const field of visualFields.value) {
      if (field.enabled && !field.name.trim()) {
        ElMessage.error('请填写所有启用字段的名称');
        return;
      }
    }

    // 计算启用的字段用于查询映射配置
    const enabledFields = visualFields.value.filter((field: any) => field.enabled);
    const searchableFields = enabledFields.filter((field: any) => field.searchable).map((field: any) => field.name);
    const filterableFields = enabledFields.filter((field: any) => field.filterable).map((field: any) => field.name);
    const rangeableFields = enabledFields.filter((field: any) => field.rangeable).map((field: any) => field.name);
    const sortableFields = enabledFields.filter((field: any) => field.sortable).map((field: any) => field.name);

    // 构造排序字段配置
    const sortFieldsConfig: Record<string, string> = {};
    enabledFields
      .filter((field: any) => field.sortable && field.sortOrder)
      .forEach((field: any) => {
        sortFieldsConfig[field.name] = field.sortOrder;
      });

    const result = {
      visualFields: enabledFields.map(field => {
        // 只保留必要字段信息，移除查询相关属性
        return {
          name: field.name,
          type: field.type,
          comment: field.comment,
          enabled: field.enabled
        };
      }),
      queryMapping: {
        fuzzy_search_fields: searchableFields,
        exact_match_fields: filterableFields,
        range_query_fields: rangeableFields,
        allowed_sort_fields: sortableFields,
        sort_fields_config: sortFieldsConfig
      }
    };

    console.log('🔍 保存可视化配置：', result);

    // 调用父抽屉的回调函数
    if ((window as any)?.handleVisualConfigResult) {
      (window as any).handleVisualConfigResult(result);
    } else if ((window.parent as any)?.handleVisualConfigResult) {
      (window.parent as any).handleVisualConfigResult(result);
    } else {
      console.error('❌ 未找到回调函数 handleVisualConfigResult');
    }

    ElMessage.success('配置保存成功');
    drawerMessenger.hideDrawer(true);  // 关闭第二层抽屉
  } catch (error) {
    console.error('❌ 保存配置失败:', error);
    ElMessage.error('保存配置失败');
  }
};

// 更新第二层抽屉底部按钮配置
const updateDrawerButtons = () => {
  console.log('🔧 InterfaceVisualConfigForm updateDrawerButtons 被调用');
  try {
    const rightButtons = [
      {
        text: '取消',
        handler: handleCancel
      },
      {
        text: '保存配置',
        type: 'primary' as const,
        handler: handleSave
      }
    ];

    // 更新第二层抽屉store中的按钮配置
    globalDrawerStore.secondLeftButtons = []; // 第二层抽屉没有左侧按钮
    globalDrawerStore.secondRightButtons = rightButtons;
    console.log('✅ 第二层抽屉按钮配置更新成功:', rightButtons);
  } catch (error) {
    console.error('❌ updateDrawerButtons 错误:', error);
  }
};

// 监听第二层抽屉属性变化
watch(() => globalDrawerStore.secondProps, async (newProps) => {
  console.log('可视化配置抽屉接收到props：', newProps);
  if (newProps && newProps.editData) {
    console.log('开始加载字段配置，editData：', newProps.editData);
    await loadFieldsFromOrmConfig();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });

// 从数据表加载字段
const handleLoadFromTable = async () => {
  loadingFromTable.value = true;
  try {
    await loadFieldsFromOrmConfig();
    ElMessage.success('已从数据表重新加载字段配置');
  } catch (error) {
    console.error('从数据表加载字段失败:', error);
    ElMessage.error('从数据表加载字段失败');
  } finally {
    loadingFromTable.value = false;
  }
};

// 比较字段差异

// 计算表格高度
const calculateTableHeight = () => {
  // 获取视口高度
  const viewportHeight = window.innerHeight;
  // 抽屉的顶部偏移（通常是抽屉标题栏的高度）
  const drawerHeaderHeight = 60;
  // 顶部区域的高度（配置提示 + 数据源信息）
  const headerSectionHeight = 120;
  // 底部按钮区域的高度
  const footerHeight = 80;
  // 内边距和边距
  const padding = 40;

  // 计算表格可用高度
  const availableHeight = viewportHeight - drawerHeaderHeight - headerSectionHeight - footerHeight - padding;

  // 设置最小高度和最大高度
  const minHeight = 300;
  const maxHeight = 600;

  tableHeight.value = Math.max(minHeight, Math.min(maxHeight, availableHeight));

  console.log('🔍 表格高度计算:', {
    viewportHeight,
    availableHeight,
    finalHeight: tableHeight.value
  });
};

onMounted(async () => {
  await loadDataSources(); // 加载数据源列表
  await loadFieldsFromOrmConfig();
  updateDrawerButtons(); // 初始化按钮配置

  // 计算表格高度
  calculateTableHeight();

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight);
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight);
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.visual-config-container {
  /* 抽屉容器布局 */
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;

  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-bottom: 80px; /* 为DrawerFooter留出空间 */
    overflow: hidden; /* 防止整体滚动 */
  }

  .header-section {
    flex-shrink: 0; /* 顶部区域不收缩 */
    margin-bottom: 16px;
  }

  

  .datasource-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .datasource-info {
      display: flex;
      align-items: center;
    }

    .datasource-actions {
      display: flex;
      gap: 8px;
    }
  }

  .structure-change-alert {
    margin-bottom: 16px;

    .alert-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
    }
  }

  .table-container {
    flex: 1; /* 占据剩余空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 改回 hidden，由内部元素控制滚动 */
    overflow: hidden; /* 改回 hidden，由内部元素控制滚动 */

    .custom-field-actions {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      align-items: center;
      flex-shrink: 0;
    }

    .table-wrapper {
      flex: 1; /* 占据剩余空间 */
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow-x: hidden; /* 默认隐藏横向滚动条 */
      overflow-y: hidden; /* 默认隐藏纵向滚动条 */

      /* 鼠标悬停时显示滚动条 */
      &:hover {
        overflow-x: auto; /* 悬停时显示横向滚动条 */
        overflow-y: auto; /* 悬停时显示纵向滚动条 */
        
        /* 确保Element Plus表格在悬停时也能正确显示滚动条 */
        :deep(.el-table) {
          .el-table__body-wrapper {
            overflow-x: auto;
            overflow-y: auto;
          }
        }
      }
      /* 鼠标悬停时显示滚动条 */
      /* 让Element Plus表格处理滚动，但自定义滚动条样式 */
        overflow-x: auto; /* 悬停时显示横向滚动条 */
        overflow-y: auto; /* 悬停时显示纵向滚动条 */
        overflow: hidden; /* 防止双重滚动条 */

        /* 自定义Element Plus表格的滚动条样式 */
        .el-table__body-wrapper {
          /* 默认隐藏滚动条 */
          overflow-x: hidden;
          overflow-y: hidden;
          
          /* 自定义滚动条样式 */
          scrollbar-width: thin;
          scrollbar-color: rgba(157, 183, 189, 0.5) rgba(241, 245, 249, 0.3);

          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(241, 245, 249, 0.3);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(157, 183, 189, 0.5);
            border-radius: 3px;
            transition: background 0.3s ease;
          }

          &::-webkit-scrollbar-thumb:hover {
            background: rgba(157, 183, 189, 0.8);
          }

          &::-webkit-scrollbar-corner {
            background: rgba(241, 245, 249, 0.3);
          }
        }

        /* 确保表头固定 */
        .el-table__header-wrapper {
          position: sticky;
          top: 0;
          z-index: 10;
          background: #fff;
        }
      }
    }
  }



/* DrawerFooter固定在底部的样式已在page-common.scss中定义 */
</style>
