"""
Mapi-ConfigCore 主应用入口
FastAPI应用配置和启动
"""

# 首先且必须最先初始化日志系统
from app.shared.core import log_init
log_init.setup_logging()

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.shared.database import init_database, check_database_connection, cleanup_database
from app.shared.core.exception_handler import ExceptionHandler
from app.config.datasource.routers.data_source_router import router as data_source_router
from app.config.interface.routers.interface_group_router import router as interface_group_router
from app.config.interface.routers.interface_tag_router import router as interface_tag_router
from app.config.interface.routers.interface_config_router import router as interface_config_router
from app.service.dynamic_router import router as dynamic_router

# 创建FastAPI应用实例
app = FastAPI(
    title="Mapi-ConfigCore",
    description="Mapi配置管理核心服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册异常处理器
ExceptionHandler.register_handlers(app)

# 注册路由
app.include_router(
    data_source_router,
    prefix="/api/v1/datasource",
    tags=["数据源管理"]
)

app.include_router(
    interface_group_router,
    prefix="/api/v1/interface/groups",
    tags=["接口分组管理"]
)

app.include_router(
    interface_tag_router,
    prefix="/api/v1/interface/tags",
    tags=["接口标签管理"]
)

app.include_router(
    interface_config_router,
    prefix="/api/v1/interface/configs",
    tags=["接口配置管理"]
)

app.include_router(
    dynamic_router,
    prefix="",  # 不使用前缀，直接匹配用户配置的路径
    tags=["动态API服务"]
)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    import sys

    print("🚀 Mapi-ConfigCore 正在启动...", flush=True)

    # 检查数据库连接
    if check_database_connection():
        print("✅ 数据库连接正常", flush=True)
    else:
        print("❌ 数据库连接失败", flush=True)

    # 初始化数据库表
    try:
        init_database()
        print("✅ 数据库表初始化完成", flush=True)
    except Exception as e:
        print(f"❌ 数据库表初始化失败: {e}", flush=True)

    print("🎉 Mapi-ConfigCore 启动完成!", flush=True)
    print("🌐 服务器正在运行，等待请求...", flush=True)
    print("📍 本地访问: http://127.0.0.1:8000", flush=True)
    print("📍 网络访问: http://**************:8000", flush=True)
    print("📖 API文档: http://127.0.0.1:8000/docs 或 http://**************:8000/docs", flush=True)
    print("🔍 健康检查: http://127.0.0.1:8000/health 或 http://**************:8000/health", flush=True)
    sys.stdout.flush()

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print("👋 Mapi-ConfigCore 正在关闭...")

    # 清理数据库连接
    cleanup_database()

    print("✅ Mapi-ConfigCore 已安全关闭")

@app.get("/", tags=["系统"])
async def root():
    """根路径 - 系统信息"""
    return {
        "name": "Mapi-ConfigCore",
        "version": "1.0.0",
        "description": "Mapi配置管理核心服务",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health", tags=["系统"])
async def health_check():
    """健康检查"""
    db_status = check_database_connection()
    
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "timestamp": "2024-12-27T22:00:00Z"
    }

if __name__ == "__main__":
    import uvicorn
    import signal
    import sys

    def signal_handler(sig, frame):
        """信号处理器，确保优雅关闭"""
        print("\n🛑 接收到关闭信号，正在优雅关闭...")
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",  # 监听所有网络接口，支持127.0.0.1和**************
        port=8000,
        reload=False,  # 关闭热更新避免关闭缓慢
        log_level="info"
    )
