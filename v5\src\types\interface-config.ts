/**
 * 接口配置管理相关类型定义
 * 用于：InterfaceConfig.vue 接口配置管理页面
 */

// 接口配置基本信息
export interface InterfaceConfig {
  id: number;
  name: string;                    // 接口名称
  path: string;                    // 接口路径
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';  // HTTP方法
  groupId: number;                 // 所属分组ID
  groupName?: string;              // 所属分组名称（用于显示）
  datasourceId: number;            // 数据源ID
  datasourceName?: string;         // 数据源名称（用于显示）
  tableName: string;               // 数据表名
  tableType?: string;              // 数据表类型（table/view/procedure）
  description?: string;            // 接口描述
  isEnabled: boolean;              // 是否启用
  isPublic: boolean;               // 是否公开（无需认证）
  tags?: number[];                 // 标签ID数组
  tagNames?: string[];             // 标签名称数组（用于显示）
  queryFields?: string[];          // 可查询字段
  requiredFields?: string[];       // 必填字段
  responseFields?: string[];       // 响应字段

  // ORM模型配置
  ormModelConfig?: any;            // ORM模型配置
  ormModelName?: string;           // ORM模型名称
  ormRelationships?: any;          // ORM关联关系配置

  // 参数配置
  parameterConfig?: any;           // 参数配置
  visualConfig?: any;              // 可视化配置
  validationRules?: any;           // 验证规则配置

  cacheDuration?: number;          // 缓存时长（秒）
  rateLimit?: number;              // 频率限制（次/分钟）
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
  createdBy?: string;              // 创建人
  lastTestAt?: string;             // 最后测试时间
  testStatus?: 'success' | 'failed' | 'pending' | null;  // 测试状态
}

// 接口配置创建/更新请求
export interface InterfaceConfigRequest {
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  groupId: number;
  datasourceId: number;
  tableName: string;
  tableType?: string;              // 数据表类型（table/view/procedure）
  description?: string;
  isEnabled?: boolean;
  isPublic?: boolean;
  tags?: number[];
  queryFields?: string[];
  requiredFields?: string[];
  responseFields?: string[];

  // ORM模型配置
  ormModelConfig?: any;            // ORM模型配置
  ormModelName?: string;           // ORM模型名称
  ormRelationships?: any;          // ORM关联关系配置

  // 参数配置
  parameterConfig?: any;           // 参数配置
  visualConfig?: any;              // 可视化配置
  validationRules?: any;           // 验证规则配置

  cacheDuration?: number;
  rateLimit?: number;
}

// 接口配置列表查询参数
export interface InterfaceConfigQuery {
  page?: number;
  page_size?: number;              // 注意：这个字段发送给后端，保持 snake_case
  search?: string;                 // 搜索关键词（接口名称或路径）
  groupId?: number;                // 分组筛选
  datasourceId?: number;           // 数据源筛选
  method?: string;                 // HTTP方法筛选
  isEnabled?: boolean;             // 启用状态筛选
  isPublic?: boolean;              // 公开状态筛选
  tagIds?: number[];               // 标签筛选
}

// 接口配置列表响应
export interface InterfaceConfigListResponse {
  items: InterfaceConfig[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// HTTP方法选项
export interface HttpMethodOption {
  value: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  label: string;
  color: string;
}

// HTTP方法配置
export const HTTP_METHODS: HttpMethodOption[] = [
  { value: 'GET', label: 'GET', color: '#67C23A' },
  { value: 'POST', label: 'POST', color: '#409EFF' },
  { value: 'PUT', label: 'PUT', color: '#E6A23C' },
  { value: 'DELETE', label: 'DELETE', color: '#F56C6C' },
  { value: 'PATCH', label: 'PATCH', color: '#9C27B0' }
];

// 数据表字段信息
export interface TableField {
  name: string;                    // 字段名
  type: string;                    // 字段类型
  comment?: string;                // 字段注释
  isNullable: boolean;             // 是否可为空
  isPrimary: boolean;              // 是否主键
  defaultValue?: string;           // 默认值
}

// 数据表结构信息
export interface TableStructure {
  tableName: string;
  fields: TableField[];
}

// 接口测试请求
export interface InterfaceTestRequest {
  interfaceId: number;
  testParams?: Record<string, any>;    // 测试参数
  testHeaders?: Record<string, string>; // 测试请求头
}

// 接口测试响应
export interface InterfaceTestResponse {
  success: boolean;
  statusCode: number;
  responseTime: number;            // 响应时间（毫秒）
  responseData: any;               // 响应数据
  errorMessage?: string;           // 错误信息
  testTime: string;                // 测试时间
}
